<template>
    <div class="entry-container" :class="{ 'mobile-view': isMobileView }">
        <div v-if="loading" class="content loading-container">
            <b-loading>加载中</b-loading>
        </div>
        <div v-else class="content">
            <div v-if="!isMobileView && includePhoneAnswer" class="qr-code-wrap">
                <div class="qr-code-wrap-cont">
                    <p class="tit">手机端作答</p>
                    <div class="qr-code-wrap-cont-inner">
                        <MobileQrCode :url="generateExamUrl($route.path, true)" />
                    </div>
                </div>
            </div>
            <div class="exam-list-wrap">
                <!-- 调试 -->
                <div v-if="monitorStore.debugInfo?.prepareCheck" class="device-test exam-item">
                    <div class="flex">
                        <SvgIcon class="icon-side" name="end-device-test" width="48" height="48" />
                        <div class="exam-info">
                            <div class="exam-tit">
                                <p class="title">一、提前设备调试</p>
                            </div>
                            <div class="exam-time">
                                <p>
                                    时间：<span>{{ monitorStore.debugInfo.startTime }}</span> 至 <span>{{ monitorStore.debugInfo.endTime }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <CopyButton v-if="isMobileView" class="copy-btn" :text="generateExamUrl(`/exam-list?seqId=${$route.query.seqId}`, false)" />
                    <b-button v-else class="btn-wrap" shape="round" status="primary" type="outline" @click="goDebug">
                        <span class="btn-name">设备调试</span>
                        <SvgIcon name="svg-empty-arrow-caret-right" width="16" height="16" />
                    </b-button>
                </div>

                <!-- 场次下试卷列表 -->
                <div class="exam-list">
                    <div class="exam-item">
                        <div class="flex">
                            <SvgIcon class="icon-side" name="end-formal-exam" width="48" height="48" />
                            <div class="exam-info">
                                <div class="exam-tit">
                                    <p class="title"><span v-if="monitorStore.debugInfo?.prepareCheck">二、</span>开始作答</p>
                                </div>
                                <div class="exam-time">
                                    <p>请使用建议设备作答</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="list-wrap">
                        <p class="line" />
                        <template v-for="(examItem, i) in list" :key="examItem.encryptExamId">
                            <div
                                class="exam-item"
                                :class="{
                                    entry: examItem.answerStatus === AnswerStatusEnum.开始作答, // 是否是入口 结束或完成 则不算入口
                                }"
                            >
                                <div class="flex">
                                    <div class="exam-info">
                                        <div class="exam-tit exam-tit-for-exam">
                                            <p class="title">第 {{ i + 1 }} 场：{{ examItem.examName }}</p>
                                            <p
                                                v-if="!isMobileView && examItem.answerStatus !== AnswerStatusEnum.开始作答"
                                                class="status-tag"
                                                :class="[
                                                    {
                                                        'status-tag-finished': examItem.answerStatus === AnswerStatusEnum.已完成,
                                                        'status-tag-end': examItem.answerStatus === AnswerStatusEnum.已结束,
                                                    },
                                                ]"
                                            >
                                                {{ AnswerStatusEnum[examItem.answerStatus] }}
                                            </p>
                                        </div>
                                        <div class="exam-time-for-exam">
                                            <p>
                                                <span style="color: #808080">笔试时间：</span
                                                ><span class="time-text"
                                                    ><span>{{ examItem.combinationType === 2 ? '前场交卷即可开始' : examItem.startTime }}</span> 至
                                                    <span>{{ examItem.endTime }}</span></span
                                                >
                                            </p>
                                            <p v-if="examItem.adviseDurationStr">
                                                <span style="color: #808080">作答时间：</span><span class="time-text">{{ examItem.adviseDurationStr }}</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <template v-for="(item, index) in examItem.subExamList" :key="index">
                                    <div class="pc-entry">
                                        <div class="info">
                                            <SvgIcon v-if="!isMobileView" class="icon-side" name="end-formal-exam" width="48" height="48" />
                                            <p class="title" v-if="examItem.subExamList.length === 1">
                                                <span v-if="item.answerTypes.includes(2) && isMobileView"> 手机端作答 </span>
                                                <span v-else> 电脑端作答 </span>
                                            </p>
                                            <p class="title" v-else>{{ `第${index + 1}部分` }}</p>
                                        </div>
                                        <template v-if="item.answerStatus === AnswerStatusEnum.开始作答">
                                            <!-- 电脑端入口 -->
                                            <b-button v-if="!isMobileView" class="btn-wrap" shape="round" status="primary" type="outline" @click="goExam(item)">
                                                <span class="btn-name">开始作答</span>
                                                <SvgIcon name="svg-empty-arrow-caret-right" width="16" height="16" />
                                            </b-button>
                                            <!-- 移动端入口 -->
                                            <div v-else class="mobile-entry">
                                                <b-button v-if="item.answerTypes.includes(2)" shape="round" status="primary" type="primary" @click="goExam(item)">
                                                    开始作答
                                                </b-button>
                                                <CopyButton v-else class="copy-btn" :text="generateExamUrl(`/exam-list?seqId=${$route.query.seqId}`, true)" />
                                            </div>
                                        </template>
                                        <p
                                            v-else
                                            class="status-tag"
                                            :class="[
                                                {
                                                    'status-tag-finished': item.answerStatus === AnswerStatusEnum.已完成,
                                                    'status-tag-end': item.answerStatus === AnswerStatusEnum.已结束,
                                                },
                                            ]"
                                        >
                                            {{ AnswerStatusEnum[item.answerStatus] }}
                                        </p>
                                    </div>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { useMonitorStore } from '@/store/use-monitor-store';
import { generateExamUrl } from '@/utils/jump-url';
import { useIsMobile } from '@crm/exam-hooks';
import { onMounted } from 'vue';
import CopyButton from './components/copy-button.vue';
import { useExamList, AnswerStatusEnum } from './composables/use-exam-list';
import MobileQrCode from './components/mobile-qr-code.vue';

const { isMobileView } = useIsMobile();

const $route = useRoute();
const $router = useRouter();
const seqId = $route.query.seqId;
const { loading, list, includePhoneAnswer, executeFetchExamListFlow, executeNavigateToDebugFlow } = useExamList();

const monitorStore = useMonitorStore();

async function goDebug() {
    await executeNavigateToDebugFlow();
}

async function goExam(item: any) {
    const loadingToast = Toast.loading({
        content: '权限校验中...',
    });

    try {
        // 获取最新的考试信息
        const { code, message } = await monitorStore.fetchBaseInfo({
            seqId: seqId as string,
            examId: item.encryptExamId,
        });

        if (code === 0) {
            if (item.answerTypes.includes(2) && item.examH5Url && isMobileView.value) {
                jumpH5Exam(item.examH5Url);
            } else {
                const url = `/monitor?seqId=${seqId}&examId=${item.encryptExamId}`;
                $router.replace(url);
            }
        } else {
            $router.push(`/status/${seqId}?status=${code}&text=${message}`);
        }
    } catch (error: any) {
        Toast.danger('获取考试信息失败，请刷新页面后重试！');
    } finally {
        loadingToast.close();
    }
}

onMounted(() => {
    executeFetchExamListFlow();
});
</script>

<style lang="less" scoped>
.entry-container {
    height: 100%;
    padding: 8px;
    overflow: auto;

    .content {
        padding: 0 10px;
        width: 100%;
        min-height: 100%;
        background: #ffffff;
        border-radius: 8px;
    }

    .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
}

.exam-list-wrap {
    margin: 0 auto;
    width: 672px;
    padding-top: 34px;
    padding-bottom: 63px;
}

// 二维码相关
.qr-code-wrap {
    position: fixed;
    top: 106px;
    right: 20px;
    z-index: 20;
    width: 223px;
    height: 223px;
    background: #f7f7f7;
    border-radius: 12px;
    padding: 10px;

    .qr-code-wrap-cont {
        padding: 14px;
        width: 100%;
        height: 100%;
        background: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;

        .tit {
            color: #1f1f1f;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 22px;
        }

        .qr-code-wrap-cont-inner {
            width: 130px;
            height: 130px;
            margin-top: 20px;
            background-image: url(https://img.bosszhipin.com/static/zhipin/kanjian/zhice/kaoshi/qrcode-box.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

// 考试列表
.exam-list {
    margin-top: 20px;
    padding-bottom: 20px;
    background: #f7f7f7;
    border-radius: 12px;

    .list-wrap {
        padding: 0px 24px;

        .line {
            height: 1px;
            background-color: #ececec;
        }

        .exam-item {
            margin-top: 20px;
            background: #fff;
        }
    }
}

// 子项样式
.exam-item {
    padding: 26px 24px;
    background: #f7f7f7;
    border-radius: 12px;
    color: #1f1f1f;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;

    &.device-test {
        display: flex;
        align-items: center;
    }

    &.entry {
        padding: 32px;

        .exam-time {
            margin-top: 8px !important;
        }
    }

    .flex {
        flex: 1;
        display: flex;

        .exam-info {
            .exam-tit {
                display: flex;
                align-items: center;

                .title {
                    margin-right: 6px;
                }
            }

            .exam-time,
            .exam-time-for-exam {
                display: flex;
                margin-top: 6px;
                color: #4d4d4d;
                font-size: 14px;
                line-height: 18px;
                flex-wrap: wrap;

                p:first-child {
                    margin-right: 24px;
                }
                .time-text span {
                    display: inline-block;
                }
            }
            .exam-time-for-exam {
                font-size: 13px;
            }
        }
    }
    .btn-wrap {
        background: transparent;
        padding: 5.5px 8px 5.5px 15px;
        .btn-name {
            vertical-align: middle;
        }
    }
    .list-wrap {
        border: 1px solid #ececec;
    }
}

.icon-side {
    margin-right: 13px;
    flex-shrink: 0;
}

.title {
    color: #1f1f1f;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
}
.exam-tit.exam-tit-for-exam {
    .title {
        font-size: 20px;
    }
}

// 状态标签
.status-tag {
    padding: 6px 20px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    line-height: 18px;
    text-align: center;
    color: #12ada9;
    background: rgba(18, 173, 169, 0.1);
    white-space: nowrap;

    &.status-tag-finished {
        color: #12ada9;
        background: rgba(18, 173, 169, 0.1);
    }
    &.status-tag-end {
        color: #808080;
        background: rgba(197, 197, 197, 0.4);
    }
}

// 电脑端考试入口
.pc-entry {
    margin-top: 20px;
    padding: 21px 23px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #dfdfdf;
    border-radius: 12px;

    .info {
        flex: 1;
        display: flex;
        align-items: center;
    }
}

// 移动端样式
.entry-container.mobile-view {
    padding: 0;
    .content {
        border-radius: 0;
    }
    .exam-list-wrap {
        width: 100%;
    }

    .exam-item {
        padding: 20px;

        .exam-time-for-exam {
            flex-direction: column;

            p:first-child {
                margin-right: 0 !important;
            }
            p:nth-child(2) {
                margin-top: 6px;
            }
        }
    }

    .device-test {
        display: block;
        .copy-btn {
            margin-top: 12px;
            margin-left: 61px;
        }
    }

    .mobile-entry {
        display: flex;
    }
}
</style>
