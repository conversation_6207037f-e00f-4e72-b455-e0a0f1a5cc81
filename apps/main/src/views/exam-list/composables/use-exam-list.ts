import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMonitorStore } from '@/store/use-monitor-store';
import { Toast } from '@boss/design';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';
import { ExamStatusEnum } from '@crm/exam-types';
import { logger } from '@/utils/logger';

export enum AnswerStatusEnum {
    开始作答 = 1,
    已完成 = 2,
    已结束 = 3,
}

export interface IExamItem {
    encryptExamId: string; // 加密考试id
    examName: string; // 考试名称
    startTime: string; // 考试开始时间
    endTime: string; // 考试结束时间
    examSource: 0 | 2; // 关联来源 0考试 2测评
    sort: number; // 排序 从1开始
    answerTypes: (1 | 2)[]; // 作答方式 1电脑端 2手机端
    examUrl: string; // 考试链接或二维码
    answerStatus: AnswerStatusEnum; // 作答状态 1开始作答  2已完成 3已结束
    adviseDurationStr?: string; // 作答时间
    examH5Url?: string; // 考试H5链接
    combinationType: 1 | 2; // 组合类型 1 按场次时间开始 2 前场交卷即可开始
    subExamList: IExamItem[];
}

export function useExamList() {
    const loading = ref(false);
    const list = ref<IExamItem[]>([]);
    const includePhoneAnswer = ref(false);

    const route = useRoute();
    const router = useRouter();
    const monitorStore = useMonitorStore();

    const seqId = route.query.seqId || '';

    // 上报考试动作（当前考生已经确定点击了考试 埋点记录）
    async function reportEveryStep(query: { encryptExamId: string }) {
        try {
            const params = {
                ...query,
                actionType: 22, // 写死动作参数，标识列表页点击开始考试
            };

            await Invoke.exam.postStepReport(params);
        } catch (error) {
            logger.error(error);
        }
    }

    // 使用 useTrackableFlow 包装获取考试列表的逻辑
    const { execute: executeFetchExamListFlow } = useTrackableFlow<[], { success: boolean; data?: any; message?: string }>('获取考试列表流程', async ({ logStep }) => {
        loading.value = true;

        try {
            logStep('调用 getExamList API', { seqId });

            const { code, data, message } = await Invoke.examList.getExamList({
                encryptExamId: seqId,
            });

            if (code === 0) {
                logStep('更新 Store 数据', { examListCount: data.examList.length });
                list.value = data.examList.map((item: IExamItem) => {
                    const { subExamList } = item;
                    return {
                        ...item,
                        subExamList: subExamList && subExamList.length ? subExamList : [item],
                    };
                });
                includePhoneAnswer.value = data.includePhoneAnswer;
                monitorStore.debugInfo = data.debugInfo;
                return { success: true, data };
            } else {
                logStep('获取列表失败', { status: 'failure', errorMsg: message });
                return { success: false, message };
            }
        } catch (error: any) {
            logStep('请求异常', { status: 'failure', error: error.message });
            return { success: false, message: error.message };
        } finally {
            loading.value = false;
        }
    });

    async function executeNavigateToDebugFlow() {
        try {
            // 获取基础信息
            const res = await Invoke.examList.getExamList({
                encryptExamId: seqId,
            });

            if (res.code === 0) {
                // 更新调试信息
                monitorStore.debugInfo = res.data.debugInfo;
                const { canDebug } = res.data.debugInfo;

                if (canDebug) {
                    // 执行路由跳转至调试页面
                    router.push(`/monitor?seqId=${seqId}`);
                    return { success: true };
                } else {
                    // 路由跳转至调试已结束状态页面
                    router.replace(`/status/${seqId}?status=${ExamStatusEnum.调试已结束}&text=考前设备调试已结束`);
                    return { success: true, message: '调试已结束' };
                }
            } else {
                // 获取信息失败
                return { success: false, message: res.message };
            }
        } catch (error: any) {
            // 请求异常
            return { success: false, message: error.message };
        }
    }

    // 使用 useTrackableFlow 包装导航至调试流程
    // const { execute: executeNavigateToDebugFlow } = useTrackableFlow<[], { success: boolean; data?: any; message?: string }>('导航至调试流程', async ({ logStep }) => {
    //     try {
    //         logStep('获取基础信息', { seqId });

    //         const res = await Invoke.examList.getExamList({
    //             encryptExamId: seqId,
    //         });

    //         if (res.code === 0) {
    //             logStep('更新调试信息');
    //             monitorStore.debugInfo = res.data.debugInfo;
    //             const { canDebug } = res.data.debugInfo;

    //             if (canDebug) {
    //                 logStep('执行路由跳转至调试页面');
    //                 router.push(`/monitor?seqId=${seqId}`);
    //                 return { success: true };
    //             } else {
    //                 logStep('路由跳转至调试已结束状态页面');
    //                 router.replace(`/status/${seqId}?status=${ExamStatusEnum.调试已结束}&text=考前设备调试已结束`);
    //                 return { success: true, message: '调试已结束' };
    //             }
    //         } else {
    //             logStep('获取信息失败', { status: 'failure', errorMsg: res.message });
    //             return { success: false, message: res.message };
    //         }
    //     } catch (error: any) {
    //         logStep('请求异常', { status: 'failure', error: error.message });
    //         return { success: false, message: error.message };
    //     }
    // });

    // 使用 useTrackableFlow 包装导航至考试流程
    const { execute: executeNavigateToExamFlow } = useTrackableFlow<[IExamItem], { success: boolean; data?: any; message?: string }>(
        '导航至考试/调试流程',
        async ({ logStep }, item) => {
            const loadingToast = Toast.loading({
                content: '权限校验中...',
            });

            try {
                logStep('获取基础信息', { examName: item.examName, examId: item.encryptExamId });

                // 获取最新的考试信息
                const { code, message } = await monitorStore.fetchBaseInfo({
                    seqId: seqId as string,
                    examId: item.encryptExamId,
                });

                if (code === 0) {
                    logStep('上报步骤', { encryptExamId: item.encryptExamId });

                    // 上报考试动作（当前考生已经确定点击了考试 埋点记录）
                    await reportEveryStep({
                        encryptExamId: item.encryptExamId,
                    });

                    loadingToast.close();

                    // if (item.answerTypes.includes(2) && item.examH5Url && isMobileView.value) {
                    //     logStep('执行 H5 跳转', { hasH5Url: !!item.examH5Url });
                    //     jumpH5Exam(item.examH5Url);
                    //     return { success: true };
                    // } else {
                    //     logStep('执行路由跳转', { examUrl: item.examUrl });
                    //     router.push(item.examUrl);
                    // }

                    return { success: true };
                } else {
                    logStep('跳转至状态页面', { status: 'failure', errorMsg: message });
                    loadingToast.close();
                    router.push(`/status/${seqId}?status=${code}&text=${message}`);
                    return { success: false, message };
                }
            } catch (error: any) {
                logStep('请求异常', { status: 'failure', error: error.message });
                loadingToast.close();
                Toast.danger('获取考试信息失败，请刷新页面后重试！');
                return { success: false, message: error.message };
            }
        },
        {
            transformArgsForLogging: ([item]) => {
                // 只记录必要的信息
                if (!item) return [undefined];
                return [
                    {
                        examId: item.encryptExamId,
                        examName: item.examName,
                        answerStatus: AnswerStatusEnum[item.answerStatus],
                        hasH5Url: !!item.examH5Url,
                    },
                ];
            },
        },
    );

    return {
        loading,
        list,
        includePhoneAnswer,
        executeFetchExamListFlow,
        executeNavigateToDebugFlow,
        executeNavigateToExamFlow,
    };
}
