<template>
    <div class="status-home-page">
        <MonoStatus :status="status" :text="text" @onButtonClick="goListPage" @onTimeout="goListPage" />
    </div>
</template>

<script setup lang="ts">
import type { ExamStatusEnum } from '@crm/exam-types';
import { MonoStatus } from '@crm/exam-components';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

defineOptions({
    name: 'StatusHomePage',
});
const route = useRoute();
const router = useRouter();
const status = ref<ExamStatusEnum>(Number(route.query.status ?? 0));
const text = ref<string>(String(route.query.text ?? ''));
function goListPage() {
    router.replace(`/exam-list?seqId=${route.query.seqId}`);
}
</script>

<style lang="less" scoped>
.status-home-page {
    --margin-top: 12px;

    display: flex;
    justify-content: center;
    margin-top: var(--margin-top);
    margin-left: 10px;
    margin-right: 10px;
    padding-top: 102px;
    border-radius: 8px;
    min-height: calc(100vh - 64px - var(--margin-top));
    background-color: #ffffff;
}
</style>
