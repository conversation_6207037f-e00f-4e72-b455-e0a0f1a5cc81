// 交卷
import { exitFull } from '@crm/exam-utils';
import { ExamStatusEnum } from '@crm/exam-types';
import router from '@/router';
import { useMonitorStore } from '@/store/use-monitor-store';
import { ReplayTypeEnum } from '../answering/type';

const monitorStore = useMonitorStore();

export type CommitPaperType = 'manual' | 'force' | 'timeout' | 'switchScreenForce' | 'reLogin';
export type SubmitParams = {
    examId: string;
    seqId: string;
    type: CommitPaperType;
};

const getTypeConfig = (typeStr: CommitPaperType, number: number = 0) => {
    const typeKey = typeStr || 'manual';
    const config = {
        manual: {
            remarks: '', // 为空 为手动交卷 服务端根据 remarks 值区分交卷方式
            text: `您已完成该场考试，剩余${number}场待完成`,
            evaluationRemarks: 'click_paper_submit',
            commitType: 1,
        },
        force: {
            remarks: '监考判定作弊强制交卷',
            text: '监考官已强制交卷',
            evaluationRemarks: '',
            commitType: 0,
        },
        timeout: {
            remarks: '考试结束考生未交卷系统自动交卷',
            text: '考试时间已结束，系统已自动交卷',
            evaluationRemarks: 'paper_auto_submit',
            commitType: 2,
        },
        switchScreenForce: {
            remarks: '异常退出或切屏次数过多强制交卷',
            text: '检测到切换屏幕次数过多，系统已强制交卷',
            evaluationRemarks: 'paper_auto_submit',
            commitType: 3,
        },
        reLogin: {
            remarks: '重新登录强制交卷',
            text: '检测到重新登录，系统已强制交卷',
            evaluationRemarks: '',
            commitType: 0,
        },
    };

    return config[typeKey];
};

export const commitPaper = async ({ type, examId, seqId }: SubmitParams) => {
    try {
        // 查看当前是否是考试
        const examType = monitorStore.examBaseInfo.examInfo?.examType;

        let res: any = null;

        // 考试交卷
        if (examType === ReplayTypeEnum.Exam) {
            const { remarks } = getTypeConfig(type, 0);
            const paramsData = {
                encryptExamId: examId,
                remarks,
            };
            res = await Invoke.exam.postCommitPaper(paramsData);
        } else {
            // 非考试交卷
            const { evaluationRemarks, commitType } = getTypeConfig(type, 0);
            if (!evaluationRemarks) {
                Toast.danger('交卷失败, 参数错误');
                return false;
            }

            const paramsData = {
                encryptExamId: examId,
                remarks: evaluationRemarks,
                commitType,
            };

            res = await Invoke.common.postEvaluationPaperCommit(paramsData);
        }

        if (res.code === 0) {
            const { text } = getTypeConfig(type, res.data.pendingCount);

            router.replace(`/status/${seqId}?status=${ExamStatusEnum.已交卷}&text=${text}`);

            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-paper-commit', {
                pData: {
                    type: TrackTypeEnum.成功,
                    message: type,
                    nameZh: '交卷成功',
                },
            });
        } else {
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-paper-commit', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: type,
                    errorText: res,
                    nameZh: '交卷失败',
                },
            });
            // 埋点结束
        }
        exitFull();
    } catch (error: any) {
        // 埋点开始
        BossAnalyticsTrack('zhice-pc-exam-paper-commit', {
            pData: {
                type: TrackTypeEnum.失败,
                message: type,
                errorText: error.message || error.name,
                nameZh: '交卷出错',
            },
        });

        // 埋点结束
        router.replace(`/status/${seqId}?status=${ExamStatusEnum.已交卷}&text=交卷过程中出现错误，请联系监考官`);
    }
};
