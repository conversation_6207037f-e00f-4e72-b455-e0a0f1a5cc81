<template>
    <div class="verification-pc" :class="{ 'verification-pc-no-overflow': status === 'IN_RECORDING' && isCollecting }">
        <div v-if="status === 'NOT_INIT'" class="verification-step">
            <SvgIcon class="svg-icon-face-detect" name="svg-empty-face-detect" width="50" height="50" />
            <b-button type="primary" shape="round" @click.prevent="startRecording(1)"> 开始录制视频 </b-button>
        </div>
        <div v-else-if="status === 'IN_RECORDING' || isMachine" class="verification-step">
            <div class="collect-wrap">
                <div
                    class="collect-box"
                    :style="isMachine ? { backgroundImage: `url(${getDisplayUrl(lastFramePic || pcRecognizeFileUri)})` } : {}"
                    :class="{ 'collect-box-hasbg': isMachine }"
                >
                    <!-- 录制中 -->
                    <template v-if="status === 'IN_RECORDING'">
                        <div id="nebular" />
                        <div v-if="hintMessage" class="tips">
                            {{ hintMessage }}
                        </div>
                    </template>
                    <!-- 审核中 -->
                    <div v-else-if="status === 'MACHINE_VERIFYING'" class="collect-result">
                        <SvgIcon name="svg-empty-loading-classic" width="18" height="18" class="animate_rotate" />
                        <div class="result-text">加急审核中，请稍候…</div>
                    </div>
                    <!-- 有结果 -->
                    <div v-else-if="status === 'MACHINE_SUCCESS' || status === 'MACHINE_FAIL'" class="collect-result end">
                        <SvgIcon :name="status === 'MACHINE_SUCCESS' ? 'svg-full-face-detect-success' : 'svg-full-face-detect-fail'" width="44" height="44" />
                        <div class="status-text">
                            {{ status === 'MACHINE_SUCCESS' ? '验证成功' : '验证失败' }}
                        </div>
                        <template v-if="status === 'MACHINE_FAIL'">
                            <b-button v-if="recognizeFaceFailedCount >= MAX_MACHINE_VERIFY_TIMES" type="primary" shape="round" @click.prevent="apply"> 申请人工认证 </b-button>
                            <b-button v-else type="primary" shape="round" @click="startRecording(2)"> 重新验证 </b-button>
                        </template>
                    </div>
                </div>
                <!-- 录制中 进度动画 -->
                <RotateSvg v-if="status === 'IN_RECORDING' && isCollecting" class="animate_rotate collect-rotate" />
            </div>
        </div>
        <div v-else-if="status === 'MANUAL_VERIFYING' || status === 'MANUAL_SUCCESS' || status === 'MANUAL_FAIL'" class="manual verification-step">
            <div class="inner">
                <img :src="getDisplayUrl(manualApplyAttachmentUrl)" alt="" />
            </div>
            <div class="desc">
                <template v-if="status === 'MANUAL_VERIFYING'">
                    <SvgIcon name="svg-empty-loading-classic" width="18" height="18" class="animate_rotate" />
                    <div class="result-text">加急审核中，请稍候…</div>
                    <b-button type="primary" shape="round" @click.prevent="apply"> 查看 </b-button>
                </template>
                <template v-else>
                    <SvgIcon :name="status === 'MANUAL_SUCCESS' ? 'svg-full-face-detect-success' : 'svg-full-face-detect-fail'" width="44" height="44" />
                    <div class="status-text">
                        {{ status === 'MANUAL_SUCCESS' ? '人工审核通过' : '人工审核失败' }}
                    </div>
                    <b-tooltip :content="manualAuditResultRemarks" mini contentClass="manual-tooltip">
                        <div class="fail-reason">
                            {{ manualAuditResultRemarks }}
                        </div>
                    </b-tooltip>
                    <template v-if="status === 'MANUAL_FAIL'">
                        <b-button type="primary" shape="round" @click.prevent="apply"> 重新拍摄 </b-button>
                    </template>
                </template>
            </div>
        </div>
        <!-- 人工审弹窗 -->
        <ManualDialog v-if="show" v-model="show" v-bind="{ manualAuditStatus, manualAuditResult, manualApplyAttachmentUrl }" />
    </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { _fileUpload, _recognizeFace } from '@/services/apis/exam';
import { DEPLOY_ENV } from '@/shared';

import { dataURLtoFile, getFormData } from '@crm/exam-utils';
import { computed, inject, nextTick, onMounted, ref, watch } from 'vue';
import { MAX_MACHINE_VERIFY_TIMES } from '../constant';
import { useNebulaMatrix } from '../hooks/useNebulaMatrix';
import { useStatus } from '../hooks/useStatus';
import ManualDialog from './manual-dialog.vue';
import RotateSvg from './rotate-svg.vue';
import { useDebuggingStore } from '../../../../store';

const props = defineProps({
    recognizeFaceFailedCount: {
        type: Number,
        default: 0,
    },
    systemRecognizeResult: {
        type: Number,
        default: 0,
    },
    pcSystemRecognizeResult: {
        type: Number,
        default: 0,
    },
    pcRecognizeFileUri: {
        type: String,
        default: '',
    },
    manualAuditStatus: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualAuditResult: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualAuditResultRemarks: {
        type: String,
        default: '',
    },
    manualApplyAttachmentUrl: {
        type: String,
        default: '',
    },
    examId: {
        type: String,
        default: '',
    },
});
const debuggingStore = useDebuggingStore();
const isMachine = computed(() => ['MACHINE_VERIFYING', 'MACHINE_SUCCESS', 'MACHINE_FAIL'].includes(status.value));

const validateNameAndIdentity = inject<() => Promise<boolean>>('validateNameAndIdentity', () => Promise.resolve(false));

function getDisplayUrl(url: string) {
    const regex = /^https?:\/\/[^/]+/;
    const isDev = DEPLOY_ENV === 'dev';
    const result = isDev ? url.replace(regex, '') : url;
    return result;
}

// 机审最后一帧图片
const lastFramePic = ref('');
// 录制有效视频后回调
function onRecordSuccess({ file, screenshot }: { file: any; screenshot: string }) {
    status.value = 'MACHINE_VERIFYING';
    lastFramePic.value = screenshot;
    recognizeFace(file);
}
// 30s内没有发生有效的录制
function onCollectIn30SError() {
    Invoke.exam.postExamActionSave({
        encryptExamId: props.examId,
        actionType: 14,
        source: 0,
    });
    setTimeout(() => {
        updateOriginStatus();
    }, 100);
}
// 活体检测相关
const { hintMessage, isCollecting, initNebulaMatrix } = useNebulaMatrix(onRecordSuccess, onCollectIn30SError);
async function startRecording(type: 1 | 2) {
    // 1: 首次录制，2：重新录制
    if (type === 1) {
        BossAnalyticsTrack('zhice-pc-exam-identity-video', {
            pData: {
                nameZh: '首次开始录制人脸视频',
            },
        }); // 点击开始录制视频埋点
    }
    // 人身核验的前提是需要姓名&身份证信息完整
    if (!(await validateNameAndIdentity())) {
        return;
    }

    status.value = 'IN_RECORDING';
    nextTick(async () => {
        initNebulaMatrix();
    });
}

enum StatusEnum {
    NOT_INIT, // 未初始化
    IN_RECORDING, // 录制中
    MACHINE_VERIFYING, // 验证中
    MACHINE_SUCCESS, // 机审通过
    MACHINE_FAIL, // 机审不通过∏
    MANUAL_VERIFYING, // 人工审核中
    MANUAL_SUCCESS, // 人工审核通过
    MANUAL_FAIL, // 人工审核不通过
}
const status = ref('NOT_INIT');

async function getEncryptFileIdFromUpload() {
    const fd = getFormData({
        file: dataURLtoFile(lastFramePic.value, 'image'),
        biz: 'USER_EXAM_FACE_AUDIT',
    });

    try {
        const { code, data } = await _fileUpload(fd);
        return code === 0 && data?.encryptId ? data.encryptId : null;
    } catch (error) {
        return null;
    }
}

async function recognizeFace(file: any) {
    const encryptFileId = await getEncryptFileIdFromUpload();
    if (!encryptFileId) {
        status.value = 'NOT_INIT';
        return;
    }
    const params = {
        file,
        name: debuggingStore.baseInfoFormData.name,
        idCard: debuggingStore.baseInfoFormData.identity,
        async: 1,
        encryptFileId,
        encExamId: props.examId,
    };
    const fd = getFormData(params);
    try {
        const res: any = await _recognizeFace(fd);
        if (res.code === 0) {
            // startPolling();
            // (); // 测试是否会有es状态同步慢导致不准确的问题（验证会有状态同步问题，还是之前的状态值）
        } else {
            // 埋点结束
            status.value = 'MACHINE_FAIL';
            updateOriginStatus(); // 更新失败次数
        }
    } catch (error) {
        status.value = 'MACHINE_FAIL';
        updateOriginStatus();
        Toast.danger({ content: '请求异常，请稍后重试' });
    }
}

function changeStatus() {
    // 人工
    if (props.manualAuditStatus) {
        if (props.manualAuditStatus === 1) {
            status.value = 'MANUAL_VERIFYING';
        } else {
            status.value = props.manualAuditResult === 1 ? 'MANUAL_SUCCESS' : 'MANUAL_FAIL';
        }
    } else if (props.recognizeFaceFailedCount < MAX_MACHINE_VERIFY_TIMES) {
        // Handle PC system recognition results properly
        if (props.pcSystemRecognizeResult === 0) {
            status.value = 'NOT_INIT';
        } else if (props.pcSystemRecognizeResult === 1) {
            status.value = 'MACHINE_SUCCESS';
        } else if (props.pcSystemRecognizeResult === 2) {
            status.value = 'MACHINE_FAIL';
        } else if (props.pcSystemRecognizeResult === 3) {
            status.value = 'MACHINE_VERIFYING';
        } else {
            // Fallback for unexpected values
            status.value = 'NOT_INIT';
        }
    } else {
        status.value = props.systemRecognizeResult === 1 ? 'MACHINE_SUCCESS' : 'MACHINE_FAIL';
    }
}

const { updateOriginStatus } = useStatus(changeStatus);

const show = ref(false);
function apply() {
    show.value = true;
}

onMounted(() => {
    changeStatus();
});

// Watch for prop changes and update status accordingly
watch(
    () => [props.pcSystemRecognizeResult, props.systemRecognizeResult, props.recognizeFaceFailedCount, props.manualAuditStatus, props.manualAuditResult],
    () => {
        changeStatus();
    },
    { immediate: false, deep: true },
);
</script>

<style lang="less" scoped>
:global(.b-tooltip-content) {
    max-width: 500px !important;
}
.verification-pc-no-overflow {
    overflow: hidden;
}
.verification-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .svg-icon-face-detect {
        color: var(--primary-color);
        margin-bottom: 24px;
    }
    &.manual {
        position: relative;
        border-radius: 4px;
        overflow: hidden;
        &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            background: rgba(0, 0, 0, 0.4);
        }
        .inner {
            width: 300px;
            height: 180px;
            img {
                display: block;
                max-width: 100%;
                max-height: 100%;
                margin: 0 auto;
            }
        }
        .desc {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #fff;
            z-index: 1;
            svg {
                color: #fff;
                margin-bottom: 8px;
            }
            .status-text {
                font-size: 14px;
                font-weight: 500;
            }
            .fail-reason {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                word-wrap: break-word;
                word-break: break-all;
                padding: 0 30px;
                line-height: 17px;
                font-size: 12px;
                margin-top: 4px;
            }
            .b-button {
                margin-top: 12px;
            }
        }
    }
}
.collect-wrap {
    position: relative;
    width: 190px;
    height: 190px;
    .collect-rotate {
        position: absolute;
        width: 250px;
        height: 250px;
        left: -30px;
        top: -30px;
    }
}
.collect-box {
    position: relative;
    width: 174px;
    height: 174px;
    margin: 8px auto;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 0 0 8px rgba(18, 173, 169, 0.2);
    transform: rotate(0);
    z-index: 1;
    background-size: 227px 100%;
    background-position: center;
    &.collect-box-hasbg {
        &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            background-color: rgba(0, 0, 0, 0.4);
        }
    }
    #nebular {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        width: 227px;
        height: 100%;
        left: -27px;
        top: 0;
        :deep(video) {
            display: block;
            max-width: 100%;
            max-height: 100%;
        }
    }
    .tips {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        text-align: center;
        width: 100%;
        font-size: 12px;
        font-weight: 500;
        color: #fff;
        height: 34px;
    }
    .collect-result {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        // background-size: 227px 100%;
        // background-position: center;
        z-index: 1;
        &.end {
            svg {
                color: #fff;
                margin-bottom: 8px;
            }
            color: #fff;
        }
        .b-button {
            margin-top: 12px;
        }
    }
}
.result-text {
    line-height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    text-align: center;
}
.collect-result {
    svg {
        color: #939cbc;
        margin-bottom: 14px;
    }
}
</style>
