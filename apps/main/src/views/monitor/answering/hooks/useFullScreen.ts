import type { DialogReturn } from '@boss/design';
import { userCutScreenDialog } from '@/utils/system-notification-dialog';
import { beFull, exitFull, isFull } from '@crm/exam-utils/src/tools/fullscreen';
import { onUnmounted, ref } from 'vue';
import { switchScreenIncrease } from './useSwitchScreen';
import { useDebuggingStore } from '../../store';

function closeDialogs() {
    if (fullscreenDialogRef?.close) {
        try {
            fullscreenDialogRef.close();
        } catch (error) {
            logger.error('关闭对话框失败:', error);
        }
    }
    fullscreenDialogRef = null;
    isDialogShowing.value = false;
}

let fullscreenDialogRef: DialogReturn | null = null;
let isDialogShowing = ref(false);
let isProcessingFullscreenChange = ref(false);
let fullscreenCheckTimer: NodeJS.Timeout | null = null;

// 清理定时器
function clearFullscreenTimer() {
    if (fullscreenCheckTimer) {
        clearTimeout(fullscreenCheckTimer);
        fullscreenCheckTimer = null;
    }
}

// 创建对话框的辅助函数，使用 force 参数确保可以创建
async function createFullscreenDialog(): Promise<DialogReturn | null> {
    // 先确保完全清理
    closeDialogs();

    // 等待一帧确保清理完成
    await new Promise((resolve) => setTimeout(resolve, 50));

    try {
        const dialog = userCutScreenDialog(
            {
                title: '全屏考试',
                content: '请您点击下方按钮开启全屏模式进行考试，并保持考试全程为全屏模式，如果退出全屏将视为作弊。',
                type: 'info',
                showCancel: false,
                showClose: false,
                confirmText: '开启全屏',
                layerClosable: false,
                enableEscClose: false,
                async confirm() {
                    isProcessingFullscreenChange.value = true;

                    try {
                        await beFull();

                        // 埋点 开启全屏
                        BossAnalyticsTrack('zhice-pc-exam-begin-fullscreen', {
                            pData: {
                                nameZh: '开启全屏',
                            },
                        });

                        // 关闭对话框
                        isDialogShowing.value = false;
                        fullscreenDialogRef = null;
                    } catch (error) {
                        isDialogShowing.value = false;

                        // 延迟重试
                        setTimeout(() => {
                            executeAskFullFlow();
                        }, 1000);
                    } finally {
                        isProcessingFullscreenChange.value = false;
                    }
                },
                onClose() {
                    isDialogShowing.value = false;
                    fullscreenDialogRef = null;
                },
            },
            true,
        ); // force = true

        if (dialog) {
            return dialog;
        } else {
            logger.warn('userCutScreenDialog 返回值为:', dialog);
            return null;
        }
    } catch (error) {
        logger.error('userCutScreenDialog 创建对话框异常:', error);
        return null;
    }
}

const executeAskFullFlow = async () => {
    // 如果正在处理全屏变化或对话框已经显示，跳过
    if (isProcessingFullscreenChange.value || isDialogShowing.value) {
        return;
    }

    if (isFull()) {
        return;
    }

    isDialogShowing.value = true;

    try {
        fullscreenDialogRef = await createFullscreenDialog();

        if (!fullscreenDialogRef) {
            // 如果创建失败，重置状态并稍后重试
            isDialogShowing.value = false;
            setTimeout(() => {
                executeAskFullFlow();
            }, 2000);
        }
    } catch (error) {
        logger.error('创建对话框失败:', error);
        isDialogShowing.value = false;
        fullscreenDialogRef = null;

        // 重试
        setTimeout(() => {
            executeAskFullFlow();
        }, 2000);
    }
};

function askFull() {
    executeAskFullFlow();
}

const executeExitHandlerFlow = async () => {
    const isCurrentlyFull = isFull();
    if (isProcessingFullscreenChange.value) {
        return;
    }

    if (!isCurrentlyFull) {
        isProcessingFullscreenChange.value = true;

        // 关闭可能存在的对话框
        closeDialogs();

        // 埋点
        BossAnalyticsTrack('zhice-pc-exam-paper-exit-fullscreen', {
            pData: {
                type: TrackTypeEnum.失败,
                nameZh: '退出全屏',
            },
        });

        try {
            const committed = await switchScreenIncrease();

            if (!committed) {
                // 重置状态并请求全屏
                isProcessingFullscreenChange.value = false;
                setTimeout(() => {
                    askFull();
                }, 300);
            } else {
                isProcessingFullscreenChange.value = false;
            }
        } catch (error) {
            logger.error('switchScreenIncrease failed:', error);
            isProcessingFullscreenChange.value = false;

            // 出错时也要重新请求全屏
            setTimeout(() => {
                askFull();
            }, 1000);
        }
    } else {
        closeDialogs();
    }
};

// 简化的退出处理函数
async function exitHandler() {
    // 简单的防抖处理
    clearFullscreenTimer();
    fullscreenCheckTimer = setTimeout(async () => {
        await executeExitHandlerFlow();
    }, 50);
}

function bindFullScreenEvents() {
    // 绑定各种全屏事件
    document.addEventListener('webkitfullscreenchange', exitHandler, false);
    document.addEventListener('mozfullscreenchange', exitHandler, false);
    document.addEventListener('fullscreenchange', exitHandler, false);
    document.addEventListener('MSFullscreenChange', exitHandler, false);

    // 额外监听 keydown 事件，特别处理 ESC 键
    document.addEventListener('keydown', handleKeyDown, false);
}

function removeFullScreenEvents() {
    document.removeEventListener('webkitfullscreenchange', exitHandler, false);
    document.removeEventListener('mozfullscreenchange', exitHandler, false);
    document.removeEventListener('fullscreenchange', exitHandler, false);
    document.removeEventListener('MSFullscreenChange', exitHandler, false);
    document.removeEventListener('keydown', handleKeyDown, false);
    clearFullscreenTimer();
}

// 处理键盘事件，特别是 ESC 键
function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape' || event.keyCode === 27) {
        // ESC 键被按下，延迟检查全屏状态
        setTimeout(() => {
            executeExitHandlerFlow();
        }, 100);
    }
}

export default function () {
    const debuggingStore = useDebuggingStore();

    if (debuggingStore.examConfig.switchScreenRule.fullScreen === 1) {
        // 初始检查全屏状态
        setTimeout(() => {
            if (!isFull()) {
                askFull();
            }
        }, 500);
        bindFullScreenEvents();
    }

    onUnmounted(() => {
        removeFullScreenEvents();
        exitFull().catch((error) => {
            logger.warn('退出全屏失败:', error);
        });
        closeDialogs();
        clearFullscreenTimer();
    });
}
