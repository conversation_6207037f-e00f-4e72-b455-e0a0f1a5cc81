<template>
    <DragCell>
        <div class="chat-entrance">
            <div class="chat-entrance-trigger" @click="toggleChat(!showChat, 'entrance')"><SvgIcon name="svg-empty-kefu" width="24" height="24" /> 监考官</div>
            <Transition name="chat-fade">
                <Chat v-show="showChat" :showChat="showChat" @toggle="toggleChat" />
            </Transition>
        </div>
    </DragCell>
</template>
<script setup lang="ts">
import { chatService } from '@/services';
import { useMonitorStore } from '@/store/use-monitor-store';
import { emitter, formatDate } from '@crm/exam-utils';
import { useDebuggingStore } from '../store';
import Chat from './chat.vue';
import { DragCell } from '@crm/exam-components';

defineOptions({
    name: 'ChatEntry',
});

const props = defineProps<{
    examId: string;
    seqId: string;
}>();

const emit = defineEmits<{
    (e: 'forceCommitPaper'): void;
}>();

const monitorStore = useMonitorStore();
const debuggingStore = useDebuggingStore();
const showChat = ref(false);
const chatExamId = computed(() => props.examId || props.seqId);

function toggleChat(value: boolean, from?: string) {
    showChat.value = value;
    if (from === 'entrance') {
        BossAnalyticsTrack('zhice-pc-exam-chat-trigger', {
            p1: Number(value),
        });
    }
}

onMounted(() => {
    const { seqInfo, mobile } = monitorStore.examBaseInfo;
    if (chatExamId.value && mobile && seqInfo?.wsConnectSecret) {
        chatService.connect({
            examId: chatExamId.value,
            encryptMobile: mobile,
            wsConnectSecrect: seqInfo?.wsConnectSecret,
        });
        emitter.on('receiveMessage', onReceiveMessage);
    } else {
        logger.error('MQTT connection prerequisites not met.');
    }
});

onUnmounted(() => {
    try {
        chatService?.disconnect();
        emitter.off('receiveMessage', onReceiveMessage);
    } catch (error: any) {
        logger.error('Error disconnecting MQTT:', error);
    }
});

// mitt相关
function onReceiveMessage(res: any) {
    const monitorStore = useMonitorStore();
    const { encryptUserId, seqInfo } = monitorStore.examBaseInfo;
    // 统一封窗弹窗提示
    function showTipDialog({ title, content }: { title: string; content: string }) {
        Dialog.open({
            title,
            content,
            type: 'info',
            showCancel: false,
            confirmText: '知道了',
        });
    }

    const { proType, messages = [] } = res;
    const messgae = messages[0] || {};
    // 用来判断是监考官发送给我的消息
    const isMine = messgae.to?.userId === encryptUserId;
    if (!isMine) {
        return;
    }

    if (proType === 17) {
        // 17: 考前准备结束
        // 关闭RTC
        debuggingStore.closeRtcAll(chatExamId.value);
        const startTime = formatDate(seqInfo?.seqStartTime, 'HH:mm:ss');
        Dialog.open({
            title: '考前准备结束',
            content: `考前准备截止24点结束，感谢您的时间！请于明天${startTime}准时前来考试，预祝您一切顺利～`,
            layerClosable: false,
            type: 'warning',
            showConfirm: false,
            cancelText: '关闭',
            enableEscClose: false,
            showClose: false,
            beforeCancel: () => {
                window.location.reload();
                return false;
            },
        });
    } else if (proType === 4) {
        showTipDialog({
            title: '系统公告',
            content: messgae.text,
        });
    } else if (proType === 7) {
        showTipDialog({
            title: '监考官提醒',
            content: messgae.text,
        });
    } else if (proType === 2 && debuggingStore.step !== 5) {
        showTipDialog({
            title: '强制交卷',
            content: '检测到您存在作弊行为，系统已强制交卷',
        });
        emit('forceCommitPaper');
    } else if (proType === 16) {
        // V组流事件回调通知
        try {
            // 解析JSON消息
            const eventData = JSON.parse(messgae.text || '{}');
            // 如果是手机第二视角相关消息
            if (eventData.reasonDesc === '正常进房') {
                // 尝试重新连接第二视角
                debuggingStore.openPhoneMonitor().catch((err) => {});
            }
        } catch (error) {}
    }
}
</script>

<style>
@import '../index.less';
</style>
