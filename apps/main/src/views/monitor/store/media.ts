import { useMonitorStore } from '@/store/use-monitor-store';
import { useDebuggingStore } from '.';
import { urls } from '@/shared/url';
import { ExamBaseInfo, ExamConfig } from './types';
import { get } from '@/services/http';
import type { NEBULA } from '@/types/nebulartc';
import { detectScreenShareType, requestFullscreenWithScreenShare } from '@/utils/enhanced-fullscreen';

/**
 * 获取 V 组 RTC 鉴权信息 (PC 端)。
 * 请求后端接口获取加入 RTC 房间所需的 appId, signature, authorization。
 * @see https://api.weizhipin.com/project/2353/interface/api/517810 - YApi 接口文档地址
 * @param params - 请求参数对象。
 * @param params.userId - 加密后的用户 ID。
 * @returns 返回一个 Promise，包含接口响应数据。
 */
export function _getSign(params: { userId: string }) {
    return get('/wapi/web/exam/room/getSign.json', params);
}

function sendError(errType: number, source?: 1 | 2 | 3) {
    const { examBaseInfo } = useMonitorStore();
    const { seqInfo, examInfo } = examBaseInfo || {};
    const examId = examInfo?.examId || seqInfo?.seqId;

    sendErrorAction(errType, source, examId);
}

/**
 * RTC 客户端类型标识符。
 * 'camera': 用于 PC 摄像头和麦克风推流。
 * 'phone': 用于订阅手机端的音视频流。
 * 'screen': 用于 PC 屏幕共享推流。
 */
type RtcClientType = 'camera' | 'phone' | 'screen';

/**
 * RTC 鉴权信息接口。
 * 存储从 `_getSign` 接口获取的鉴权凭证。
 */
interface IRtcAuthInfo {
    /** 应用 ID，标识当前应用 */
    appId: string;
    /** 用户签名，用于 RTC 鉴权 */
    signature: string;
    /** 用户权限控制字符串，用于 RTC 鉴权 */
    authorization: string;
}

/**
 * Media 类负责管理考试过程中的 RTC (实时音视频通信) 相关逻辑。
 * 包括初始化 RTC SDK、创建和管理摄像头、手机、屏幕共享的客户端 (Client)、
 * 处理进房、推流、拉流、事件监听和状态上报等操作。
 *
 * @class Media
 */
export class Media {
    /**
     * 静态考试配置信息。
     * 从 Pinia store `useMonitorStore` 获取，包含考试相关的设置，如房间 ID 等。
     * @private
     * @static
     */
    private static examConfig: ExamConfig;
    /**
     * 静态考试基础信息。
     * 从 Pinia store `useMonitorStore` 获取，包含用户 ID 等基础信息。
     * @private
     * @static
     */
    private static examBaseInfo: ExamBaseInfo;
    /**
     * NEBULARTC SDK 的主入口实例。
     * 用于创建 RTC 客户端 (Client)。采用单例模式，延迟初始化。
     * @private
     * @static
     */
    private static rtcInstance: NEBULA.NEBULARTCInstance;
    /**
     * RTC 鉴权信息。
     * 存储从 `_getSign` 接口获取的 appId, signature, authorization。延迟初始化。
     * @private
     * @static
     */
    private static rtcAuthInfo: IRtcAuthInfo;
    /**
     * 存储不同类型的 RTC 客户端实例 (RtcClient)。
     * 使用 `RtcClientType` 作为键，值为对应的 `NEBULA.RtcClient` 实例或 null。
     * @static
     */
    public static Client: Record<RtcClientType, NEBULA.RtcClient | null> = {
        camera: null,
        phone: null,
        screen: null,
    };

    /**
     * 构造函数。
     * 初始化时从 Pinia store 获取考试配置和基础信息，并存储到静态属性中。
     */
    constructor() {
        Media.examConfig = useDebuggingStore().examConfig;
        Media.examBaseInfo = useMonitorStore().examBaseInfo;
    }

    /**
     * 获取 NEBULARTC SDK 主入口实例 (单例)。
     * 如果实例尚未创建，则进行初始化，设置环境地址和日志级别。
     * @private
     * @static
     * @getter
     * @returns {NEBULA.NEBULARTCInstance} NEBULARTC SDK 实例。
     */
    private static get rtc(): NEBULA.NEBULARTCInstance {
        if (!Media.rtcInstance) {
            // eslint-disable-next-line no-undef
            Media.rtcInstance = NEBULARTC(); // 调用全局 NEBULARTC() 函数获取实例
            Media.rtcInstance.setEnv(urls.rtcUrl); // 设置 RTC 服务环境地址
            Media.rtcInstance.Logger.setLogLevel(Media.rtcInstance.Logger.DEBUG); // 设置日志级别为 DEBUG
        }
        return Media.rtcInstance;
    }

    /**
     * 初始化 RTC 鉴权信息 (单例)。
     * 如果鉴权信息尚未获取，则调用 `_getSign` 接口获取并存储到 `rtcAuthInfo` 静态属性。
     * (保持了代码 1 的健壮性 try/catch 和 data 检查)
     * @private
     * @static
     * @async
     * @returns {Promise<void>} 无返回值。
     */
    private static async initRtcAuthInfo(): Promise<void> {
        if (!Media.rtcAuthInfo) {
            try {
                const { code, data } = await _getSign({ userId: Media.examBaseInfo.encryptUserId });
                if (code === 0 && data) {
                    // 保持 data 检查
                    const { appId, signature, authorization } = data;
                    Media.rtcAuthInfo = { appId, signature, authorization };
                } else {
                    logger.error('Failed to get RTC Auth Info:', { code, data });
                }
            } catch (error) {
                logger.error('Error fetching RTC Auth Info:', error);
            }
        }
    }

    /**
     * 获取或创建指定类型的 RTC 客户端 (RtcClient) 实例。
     * (保持了代码 1 的健壮性：检查 rtcAuthInfo, try/catch 创建过程)
     * (BossAnalyticsTrack 调用恢复至代码 2 版本)
     *
     * @private
     * @async
     * @param {RtcClientType} type - 需要获取或创建的客户端类型 ('camera', 'phone', 'screen')。
     * @param {string} [userIdPrefix=''] - 用户 ID 的前缀，用于区分不同用途的客户端 (例如 'pc', 'answer')。
     * @returns {Promise<NEBULA.RtcClient | null>} 返回对应类型的 RtcClient 实例，如果创建失败或过程中出错可能返回 null。
     */
    private async getClient(type: RtcClientType, userIdPrefix: string = ''): Promise<NEBULA.RtcClient | null> {
        const clientExists = !!Media.Client[type];

        if (!Media.Client[type]) {
            try {
                const rtc = Media.rtc;
                await Media.initRtcAuthInfo();

                // 保持代码 1 的检查
                if (!Media.rtcAuthInfo) {
                    logger.error(`Cannot create RTC client (${type}): Auth info not available.`);
                    return null;
                }

                const params: NEBULA.ClientConfig = {
                    appId: Media.rtcAuthInfo.appId,
                    userSig: Media.rtcAuthInfo.signature,
                    userAuth: Media.rtcAuthInfo.authorization,
                    roomId: Media.examConfig.monitorRoomId,
                    mode: 'meeting', // 固定为会议模式
                    userId: `${userIdPrefix}${userIdPrefix ? '_' : ''}${Media.examBaseInfo.encryptUserId}`, // 组合用户 ID
                };

                Media.Client[type] = rtc.createClient(params);

                // 为 Client 绑定错误事件监听
                Media.Client[type]?.on('error', (evt) => {
                    logger.error(`RTC Client (${type}) Error:`, evt); // 保留 logger
                    // 埋点恢复至代码 2 版本
                    BossAnalyticsTrack('zhice-pc-exam-screen-client-error', {
                        // Key 恢复
                        pData: {
                            type: TrackTypeEnum.失败,
                            message: evt, // message 恢复为原始 evt
                            reportType: type,
                            nameZh: '客户端出错',
                        },
                    });
                    // 埋点结束
                });
            } catch (error) {
                logger.error(`Failed to create RTC Client (${type}):`, error);
                Media.Client[type] = null; // 创建失败则置为 null
            }
        }
        return Media.Client[type];
    }

    /**
     * 使指定类型的 RTC 客户端加入房间。
     * (保持了代码 1 的健壮性：检查 client, try/catch)
     * @private
     * @async
     * @param {RtcClientType} type - 要加入房间的客户端类型。
     * @returns {Promise<void | undefined>} 返回 RTC SDK `join` 方法的 Promise 结果。
     */
    private joinRoom = async (type: RtcClientType): Promise<void | undefined> => {
        const client = Media.Client[type];
        // 保持代码 1 的检查
        if (!client) {
            logger.error(`Cannot join room: RTC Client (${type}) is null.`);
            return Promise.reject(new Error(`RTC Client (${type}) is null.`));
        }
        const params: NEBULA.JoinConfig = {
            roomId: Media.examConfig.monitorRoomId,
            role: type === 'phone' ? 'subscriber' : 'publisher_only',
        };

        // 保持代码 1 的 try/catch
        try {
            const result = await client.join(params);
            return result;
        } catch (error) {
            logger.error(`RTC Client (${type}) failed to join room:`, error);
            throw error; // 将错误继续向上抛出，由调用方处理
        }
    };

    /**
     * 开启 PC 端摄像头和麦克风监控。
     * 使用 useTrackableFlow 包装，以便追踪流程执行过程
     *
     * @public
     * @async
     * @param {boolean} [isVirtualTest=false] - 是否为虚拟设备测试模式。如果为 true，则不执行实际的推流操作 (`client.publish`)。
     * @returns {Promise<void>} 无返回值。
     */
    public openCameraMonitor = async (isVirtualTest: boolean = false): Promise<void> => {
        const debuggingStore = useDebuggingStore();
        const status = debuggingStore.STATUS.camera.status;
        if (status === 2) {
            return;
        }

        const client = await this.getClient('camera', 'pc');
        // 保持代码 1 对 client null 的处理
        if (!client) {
            logger.error('Failed to get/create camera client.');
            debuggingStore.updateStatus('camera', { status: 1, errorText: '初始化摄像头客户端失败' });
            sendError(402);
            return;
        }

        debuggingStore.updateStatus('camera', { client });

        try {
            await this.joinRoom('camera');
            // 摄像头客户端入房成功埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-camera-enter-room', {
                pData: {
                    type: TrackTypeEnum.成功,
                    reportType: ReportTypeEnum.摄像头,
                    nameZh: '摄像头客户端入房成功',
                },
            });
        } catch (error) {
            logger.error('Camera client failed to join room:', error); // 保留 logger
            let message = '加入房间失败';
            if (error instanceof Error) {
                message = error.message || error.name;
            }
            // 摄像头客户端入房失败埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-camera-enter-room', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: message, // 使用计算出的 message
                    reportType: ReportTypeEnum.摄像头,
                    nameZh: '摄像头客户端入房失败',
                },
            });
            // 埋点结束
            // 保持代码 1 的状态更新和错误上报
            debuggingStore.updateStatus('camera', { status: 1, errorText: '加入房间失败' });
            sendError(402);
            return;
        }

        let palyerStateIsOk = false;
        let localStream: NEBULA.RtcStream | null = null;

        try {
            localStream = await client.createStream({
                userId: `pc_${Media.examBaseInfo.encryptUserId}`,
                audio: true,
                video: true,
                dynamicBitrate: false,
                useSeparateStreams: true,
            });
            BossAnalyticsTrack('zhice-pc-exam-camera-stream-create', {
                pData: {
                    type: TrackTypeEnum.成功,
                    reportType: ReportTypeEnum.摄像头,
                    nameZh: '摄像头创建流成功',
                },
            });
        } catch (error) {
            logger.error('Failed to create camera stream:', error); // 保留 logger
            let message = '创建流失败';
            if (error instanceof Error) {
                message = error.message || error.name;
            }
            // 摄像头创建流失败埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-camera-stream-create', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: message, // 使用计算出的 message
                    reportType: ReportTypeEnum.摄像头,
                    nameZh: '摄像头创建流失败',
                },
            });
            // 埋点结束
            // 保持代码 1 的状态更新和错误上报
            debuggingStore.updateStatus('camera', { status: 1, errorText: '创建音视频流失败' });
            sendError(402);
            return;
        }

        localStream?.setVideoProfile({ width: 640, height: 360, frameRate: 10, bitrate: 550 });

        localStream
            ?.initialize()
            .then(async () => {
                BossAnalyticsTrack('zhice-pc-exam-camera-stream-init', {
                    pData: {
                        type: TrackTypeEnum.成功,
                        reportType: ReportTypeEnum.摄像头,
                        nameZh: '摄像头流初始化成功',
                    },
                });
                // 埋点结束
                localStream.setVideoContentHint('detail');

                const audioTrack = localStream.getAudioTrack();
                const videoTrack = localStream.getVideoTrack();
                const audioLabel = audioTrack?.label;
                const videoLabel = videoTrack?.label;

                // 设备label上报埋点 (与代码 2 一致)
                BossAnalyticsTrack('zhice-pc-exam-device-label', {
                    pData: {
                        type: TrackTypeEnum.成功,
                        audioLabel,
                        videoLabel,
                        reportType: ReportTypeEnum.摄像头,
                        nameZh: '设备label',
                    },
                });
                sendVirtualErrorAction(audioLabel || '', videoLabel || '');

                const publishSuccess = () => {
                    BossAnalyticsTrack('zhice-pc-exam-camera-stream-publish', {
                        pData: {
                            type: TrackTypeEnum.成功,
                            reportType: ReportTypeEnum.摄像头,
                            nameZh: '摄像头推流成功',
                        },
                    });
                    debuggingStore.updateStatus('camera', { status: 2, localStream });
                    sendError(1);
                    palyerStateIsOk = true;
                };

                try {
                    if (!isVirtualTest) {
                        await client.publish(localStream);
                    }
                    publishSuccess();
                } catch (error: unknown) {
                    logger.error('Failed to publish camera stream:', error); // 保留 logger
                    let message = '推流失败';
                    if (error instanceof Error) {
                        message = error.message || error.name;
                        // 摄像头推流失败埋点 (与代码 2 一致)
                        BossAnalyticsTrack('zhice-pc-exam-camera-stream-publish', {
                            pData: {
                                type: TrackTypeEnum.失败,
                                message: message, // 使用计算出的 message
                                reportType: ReportTypeEnum.摄像头,
                                nameZh: '摄像头推流失败',
                            },
                        });
                        // 埋点结束
                    }
                    // 保持代码 1 的状态更新和错误上报
                    debuggingStore.updateStatus('camera', { status: 1, localStream: null, errorText: '推流失败' });
                    sendError(402);
                }
            })
            .catch((error) => {
                logger.error('Failed to initialize camera stream:', error); // 保留 logger
                // 保持代码 1 的错误处理逻辑
                if (error instanceof Error) {
                    const { name, message, streamType } = error as Error & { streamType?: 'video' | 'audio' };
                    let errorText = '';
                    switch (name) {
                        case 'NotFoundError':
                            errorText = '缺少可用的摄像头/麦克风设备';
                            break;
                        case 'NotAllowedError':
                            errorText = '摄像头/麦克风权限未开启';
                            break;
                        case 'NotReadableError':
                            errorText = '无法访问摄像头/麦克风';
                            break;
                        case 'SecurityError':
                            errorText = '系统禁止访问摄像头/麦克风';
                            break;
                        case 'AbortError':
                            errorText = '摄像头/麦克风异常';
                            break;
                        case 'OverConstrainedError':
                            errorText = 'cameraId/microphoneId 参数的值无效';
                            break;
                        default:
                            errorText = '检测失败';
                            break;
                    }
                    logger.error(`Initialize error details: name=${name}, message=${message}, streamType=${streamType}, errorText=${errorText}`);
                    debuggingStore.updateStatus('camera', { status: 1, localStream: null, errorText });
                    sendInitializeErrorAction(streamType as 'video' | 'audio', name as 'NotFoundError' | 'NotAllowedError' | 'NotReadableError' | 'SecurityError');

                    // 摄像头初始化失败埋点 (与代码 2 一致)
                    BossAnalyticsTrack('zhice-pc-exam-camera-stream-init', {
                        pData: {
                            type: TrackTypeEnum.失败,
                            message: message || name,
                            text: errorText,
                            reportType: ReportTypeEnum.摄像头,
                            nameZh: '摄像头初始化失败',
                        },
                    });
                    // 埋点结束
                } else {
                    debuggingStore.updateStatus('camera', { status: 1, localStream: null, errorText: '初始化摄像头/麦克风失败' });
                    sendError(402);
                }
            });

        // --- 事件监听 ---
        localStream?.on(client.TYPE.EVENT.PLAYER_STATE_CHANGED, (evt: NEBULA.PlayerStateChangedEvent) => {
            // 保持代码 1 的状态更新和错误上报逻辑
            if (evt.state === 'STOPPED') {
                debuggingStore.updateStatus('camera', { status: 1, errorText: '用户关闭设备权限，请重新开启设备权限' });
                sendError(evt.type === 'video' ? 202 : 303);
                palyerStateIsOk = false;
            } else if (evt.state === 'PLAYING') {
                debuggingStore.updateStatus('camera', { status: 2, errorText: '' }); // 保持清空 errorText
                sendError(1);
                palyerStateIsOk = true;
            }
            // 摄像头播放状态变更埋点 (恢复至代码 2 版本)
            BossAnalyticsTrack('zhice-pc-exam-camera-play-state-change', {
                pData: {
                    type: TrackTypeEnum.失败, // 恢复为硬编码失败
                    message: evt, // 恢复为原始 evt
                    reportType: ReportTypeEnum.摄像头,
                    nameZh: '摄像头播放状态变更',
                },
            });
        });

        client.on(client.TYPE.EVENT.CONNECTION_STATE_CHANGED, (evt: NEBULA.ConnectionStateChangedEvent) => {
            // 保持代码 1 更完善的状态处理 (兼容新旧状态名)
            if (evt.state === 'disconnected' || evt.state === 'DISCONNECTED') {
                debuggingStore.updateStatus('camera', { status: 1, errorText: '网络异常，请刷新页面重试' });
                sendError(402);
            } else if ((evt.state === 'reconnected' || evt.state === 'CONNECTED') && palyerStateIsOk) {
                debuggingStore.updateStatus('camera', { status: 2, errorText: '' }); // 保持清空 errorText
                sendError(1);
            }
            // 网络连接变化埋点 (恢复至代码 2 版本)
            BossAnalyticsTrack('zhice-pc-exam-camera-connection-change', {
                pData: {
                    type: TrackTypeEnum.失败, // 恢复为硬编码失败
                    message: evt.state, // 保持 evt.state
                    // prevState: evt.prevState, // 移除 prevState
                    reportType: ReportTypeEnum.摄像头,
                    nameZh: '网络连接变化', // 恢复 nameZh
                },
            });
            // 埋点结束
        });
    };

    /**
     * 开启手机摄像头监控 (PC 端作为订阅方)。
     * 使用 useTrackableFlow 包装，以便追踪流程执行过程
     *
     * @public
     * @async
     * @param {boolean} [isVirtualTest=false] - (未使用)
     * @returns {Promise<void>} 无返回值。
     */
    public openPhoneMonitor = async (isVirtualTest: boolean = false): Promise<void> => {
        const debuggingStore = useDebuggingStore();
        const currentPhoneStatus = debuggingStore.STATUS.phone.status;
        const existingPhoneClient = Media.Client['phone'];

        if (currentPhoneStatus === 2 && existingPhoneClient && !isVirtualTest) {
            logger.warn('Phone monitor already opened and stream received. Verifying state.');
            // 也许这里可以加一些逻辑来确认流是否还在，但暂时先不修改核心流程
            // return; // 暂时不直接返回，让流程继续，以便观察重复绑定的影响（如果有）
        }

        const client = await this.getClient('phone', '');
        if (!client) {
            logger.error('Failed to get/create phone client for openPhoneMonitor.');
            debuggingStore.updateStatus('phone', { status: 1, errorText: '初始化手机监控客户端失败' });
            return;
        }

        debuggingStore.updateStatus('phone', { client });

        const phoneUserId = `phone_${Media.examBaseInfo.encryptUserId}`;

        try {
            await this.joinRoom('phone');
            client.subscribeUserRemoteStream({ userId: phoneUserId });
            BossAnalyticsTrack('zhice-pc-exam-phone-enter-room', {
                pData: {
                    type: TrackTypeEnum.成功,
                    reportType: ReportTypeEnum.手机,
                    nameZh: '手机客户端入房成功',
                },
            });
            logger.warn('Current phone client instance after joinRoom:', client, client);

            debuggingStore.updateStatus('phone', { status: 0, client });
        } catch (error) {
            logger.error('Phone client failed to join room in openPhoneMonitor:', error);
            let message = '加入房间失败';
            if (error instanceof Error) {
                message = error.message || error.name;
            }
            // 手机客户端入房失败埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-phone-enter-room', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: message, // 使用计算出的 message
                    reportType: ReportTypeEnum.手机,
                    nameZh: '手机客户端入房失败',
                },
            });
            // 埋点结束
            // 保持代码 1 的状态更新
            debuggingStore.updateStatus('phone', { status: 1, errorText: '加入房间失败' });
            return;
        }

        const updateRemoteStream = (remoteStream: NEBULA.RtcStream) => {
            debuggingStore.updateStatus('phone', { remoteStream });
        };

        const onSuccess = (remoteStream: NEBULA.RtcStream, type: 'add' | 'update') => {
            // 修改判断条件，只要有视频流就认为成功，不再强制要求音频流
            if (remoteStream.hasVideo()) {
                debuggingStore.updateStatus('phone', { status: 2, errorText: '' });
                // 确保更新remoteStream
                updateRemoteStream(remoteStream);
            } else {
                let errorText = '';
                if (!remoteStream.hasVideo()) errorText += '手机未开启视频 ';
                if (!remoteStream.hasAudio()) errorText += '手机未开启音频';
                debuggingStore.updateStatus('phone', { status: 1, errorText: errorText.trim() || '手机流异常' });
            }
            // 远端流add、更新埋点 (恢复至代码 2 版本)
            BossAnalyticsTrack('zhice-pc-exam-phone-stream-status', {
                pData: {
                    type: TrackTypeEnum.成功,
                    message: type,
                    // hasAudio: remoteStream.hasAudio(), // 移除 hasAudio
                    // hasVideo: remoteStream.hasVideo(), // 移除 hasVideo
                    reportType: ReportTypeEnum.手机,
                    nameZh: '远端流add、更新', // 恢复 nameZh
                },
            });
            // 埋点结束
        };

        const onError = (reason: string = '手机停止推流或连接断开') => {
            logger.warn(`PHONE_ON_ERROR_CALLED: Reason: ${reason}, clientId: ${client}`);
            // 保持代码 1 的状态更新
            debuggingStore.updateStatus('phone', { status: 1, remoteStream: null, errorText: reason });
            // 手机停止推流埋点 (恢复至代码 2 版本)
            BossAnalyticsTrack('zhice-pc-exam-phone-stream-status', {
                pData: {
                    type: TrackTypeEnum.失败,
                    // message: reason, // 移除 message
                    reportType: ReportTypeEnum.手机,
                    nameZh: '手机停止推流', // 恢复 nameZh
                },
            });
            // 埋点结束
        };

        client.on(client.TYPE.EVENT.STREAM_ADDED, (evt: NEBULA.StreamEvent) => {
            const remoteStream = evt.stream;
            if (evt.userId === phoneUserId) {
                // 保持代码 1 的修正：使用 then/catch 处理 subscribe
                client
                    .subscribe(remoteStream, { audio: true, video: true })
                    .then(() => {
                        onSuccess(remoteStream, 'add'); // 调用恢复埋点版本的 onSuccess
                    })
                    .catch((err) => {
                        onError('订阅手机流失败'); // 调用恢复埋点版本的 onError
                    });
            } else {
                // 即使不是预期用户，也尝试订阅 - 有时用户ID可能会有变化
                client
                    .subscribe(remoteStream, { audio: true, video: true })
                    .then(() => {
                        onSuccess(remoteStream, 'add');
                    })
                    .catch((err) => {
                        logger.error(`Failed to subscribe to unexpected stream: ${remoteStream}`, err);
                    });
            }
        });

        client.on(client.TYPE.EVENT.STREAM_REMOVED, (evt: NEBULA.StreamEvent) => {
            if (evt.userId === phoneUserId) {
                onError('手机停止推流'); // 调用恢复埋点版本的 onError
            }
        });

        client.on(client.TYPE.EVENT.STREAM_UPDATED, (evt: NEBULA.StreamEvent) => {
            const remoteStream = evt.stream;
            if (evt.userId === phoneUserId) {
                onSuccess(remoteStream, 'update'); // 调用恢复埋点版本的 onSuccess
            }
        });

        client.on(client.TYPE.EVENT.STREAM_SUBSCRIBED, (evt: NEBULA.StreamEvent) => {
            const remoteStream = evt.stream;
            // 无论userId如何，都更新流
            updateRemoteStream(remoteStream);
            // 订阅手机流成功埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-phone-stream-subscribe', {
                pData: {
                    type: TrackTypeEnum.成功,
                    reportType: ReportTypeEnum.手机,
                    nameZh: '订阅手机流成功',
                },
            });
            // 埋点结束
        });

        // 保持代码 1 增加的 CONNECTION_STATE_CHANGED 监听逻辑，但移除其内部的埋点调用
        client.on(client.TYPE.EVENT.CONNECTION_STATE_CHANGED, (evt: NEBULA.ConnectionStateChangedEvent) => {
            // 保持代码 1 的逻辑
            if (evt.state === 'disconnected' || evt.state === 'DISCONNECTED') {
                onError('PC 网络连接断开'); // 调用恢复埋点版本的 onError (其内部已无 message)
            }
            // 此处原代码 1 有埋点，根据要求移除
            // BossAnalyticsTrack(...)
        });
    };

    /**
     * 开启 PC 端屏幕共享监控。
     * 使用 useTrackableFlow 包装，以便追踪流程执行过程
     *
     * @public
     * @async
     * @param {boolean} [isVirtualTest=false] - 是否为虚拟测试模式。
     * @returns {Promise<void>} 无返回值。
     */
    public openScreenMonitor = async (isVirtualTest: boolean = false): Promise<void> => {
        const debuggingStore = useDebuggingStore();
        const status = debuggingStore.STATUS.screen.status;
        if (status === 2) {
            logger.warn('Screen monitor already opened.');
            return;
        }

        const client = await this.getClient('screen', 'answer');
        // 保持代码 1 对 client null 的处理
        if (!client) {
            logger.error('Failed to get/create screen client.');
            debuggingStore.updateStatus('screen', { status: 1, errorText: '初始化屏幕共享客户端失败' });
            sendError(402, 3);
            return;
        }

        debuggingStore.updateStatus('screen', { client });

        try {
            if (!isVirtualTest) {
                await this.joinRoom('screen');
                // 屏幕共享客户端入房成功埋点 (与代码 2 一致)
                BossAnalyticsTrack('zhice-pc-exam-screen-enter-room', {
                    pData: {
                        type: TrackTypeEnum.成功,
                        reportType: ReportTypeEnum.屏幕共享,
                        nameZh: '屏幕共享客户端入房成功',
                    },
                });
            } else {
                logger.warn('Virtual test mode: Skipping screen client join room.'); // 保留 logger
            }
        } catch (error) {
            logger.error('Screen client failed to join room:', error); // 保留 logger
            let message = '加入房间失败';
            if (error instanceof Error) {
                message = error.message || error.name;
            }
            // 屏幕共享客户端入房失败埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-screen-enter-room', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: message, // 使用计算出的 message
                    reportType: ReportTypeEnum.屏幕共享,
                    nameZh: '屏幕共享客户端入房失败',
                },
            });
            // 埋点结束
            // 保持代码 1 的状态更新和错误上报
            debuggingStore.updateStatus('screen', { status: 1, errorText: '加入房间失败' });
            sendError(402, 3);
            return;
        }

        let localStream: NEBULA.RtcStream | null = null;
        let palyerStateIsOk = false;

        try {
            // 保持代码 1 更明确的参数，并添加屏幕共享约束
            localStream = await client.createStream({
                userId: `answer_${Media.examBaseInfo.encryptUserId}`,
                screen: true,
                audio: false,
                video: true,
                dynamicBitrate: false,
                // 添加屏幕共享约束，优先选择整个屏幕
                screenConstraints: {
                    video: {
                        displaySurface: 'monitor', // 优先选择显示器
                        logicalSurface: true,
                        cursor: 'always'
                    }
                }
            });
            // 屏幕共享创建流成功埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-screen-stream-create', {
                pData: {
                    type: TrackTypeEnum.成功,
                    reportType: ReportTypeEnum.屏幕共享,
                    nameZh: '屏幕共享创建流成功',
                },
            });
        } catch (error) {
            logger.error('Failed to create screen stream:', error); // 保留 logger
            let message = '创建流失败';
            // 保持代码 1 对 NotSupportedError 的检查
            if (error instanceof DOMException && error.name === 'NotSupportedError') {
                message = '浏览器不支持屏幕共享';
            } else if (error instanceof Error) {
                message = error.message || error.name;
            }
            // 屏幕共享创建流失败埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-screen-stream-create', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: message, // 使用计算出的 message
                    reportType: ReportTypeEnum.屏幕共享,
                    nameZh: '屏幕共享创建流失败',
                },
            });
            // 埋点结束
            // 保持代码 1 的状态更新和错误上报
            debuggingStore.updateStatus('screen', { status: 1, errorText: message });
            sendError(402, 3);
            return;
        }

        localStream?.setScreenProfile({ width: 1280, height: 720, frameRate: 5, bitrate: 400 });

        const publishSuccess = () => {
            // 屏幕共享推流成功埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-screen-stream-publish', {
                pData: {
                    type: TrackTypeEnum.成功,
                    reportType: ReportTypeEnum.屏幕共享,
                    nameZh: '屏幕共享推流成功',
                },
            });
            // 埋点结束 (注意：代码 2 在这里没有 // 埋点结束 注释，保持一致)
            // 保持代码 1 的状态更新和错误上报
            debuggingStore.updateStatus('screen', { status: 2, localStream, errorText: '' });
            sendError(1, 3);
            palyerStateIsOk = true;
        };

        localStream
            ?.initialize()
            .then(async () => {
                // 屏幕共享流初始化成功埋点 (与代码 2 一致)
                BossAnalyticsTrack('zhice-pc-exam-screen-stream-init', {
                    pData: {
                        type: TrackTypeEnum.成功,
                        reportType: ReportTypeEnum.屏幕共享,
                        nameZh: '屏幕共享流初始化成功',
                    },
                });
                // 埋点结束
                localStream.setVideoContentHint('detail');

                // 检测屏幕共享类型并处理全屏逻辑
                await this.handleScreenShareAndFullscreen(localStream);

                try {
                    if (!isVirtualTest) {
                        await client.publish(localStream);
                    } else {
                        logger.warn('Virtual test mode: Skipping screen stream publish.'); // 保留 logger
                    }
                    publishSuccess();
                } catch (error: unknown) {
                    logger.error('Failed to publish screen stream:', error); // 保留 logger
                    let message = '推流失败';
                    if (error instanceof Error) {
                        message = error.message || error.name;
                        // 屏幕共享推流失败埋点 (与代码 2 一致)
                        BossAnalyticsTrack('zhice-pc-exam-screen-stream-publish', {
                            pData: {
                                type: TrackTypeEnum.失败,
                                message: message, // 使用计算出的 message
                                reportType: ReportTypeEnum.屏幕共享,
                                nameZh: '屏幕共享推流失败',
                            },
                        });
                        // 埋点结束
                    }
                    // 保持代码 1 的状态更新和错误上报
                    debuggingStore.updateStatus('screen', { status: 1, localStream: null, errorText: '推流失败' });
                    sendError(402, 3);
                }
            })
            .catch((error) => {
                logger.error('Failed to initialize screen stream:', error); // 保留 logger
                // 保持代码 1 的错误处理逻辑
                if (error instanceof Error) {
                    const { name, message } = error;
                    let errorText = '';
                    // 使用代码 1 更细致的判断逻辑
                    if (message === 'Permission denied by system' || name === 'NotAllowedError') {
                        errorText = '屏幕共享权限未开启或被拒绝';
                    } else if (message === 'Permission denied') {
                        errorText = '用户取消了屏幕共享';
                    } else if (name === 'NotReadableError') {
                        errorText = '无法共享屏幕，请确保当前无其他应用正在共享屏幕';
                    } else if (name === 'SecurityError') {
                        errorText = '系统禁止共享屏幕，请取消限制后再检测';
                    } else if (name === 'AbortError') {
                        errorText = '建议更换浏览器或设备后重新检测';
                    } else {
                        errorText = '共享屏幕异常';
                    }
                    logger.error(`Initialize screen error details: name=${name}, message=${message}, errorText=${errorText}`);
                    debuggingStore.updateStatus('screen', { status: 1, localStream: null, errorText });
                    sendError(203, 3); // 保持代码 1 的错误码

                    // 屏幕共享流初始化失败埋点 (与代码 2 一致)
                    BossAnalyticsTrack('zhice-pc-exam-screen-stream-init', {
                        pData: {
                            type: TrackTypeEnum.失败,
                            message: message || name,
                            text: errorText, // 保持 text
                            reportType: ReportTypeEnum.屏幕共享,
                            nameZh: '屏幕共享流初始化失败',
                        },
                    });
                    // 埋点结束
                } else {
                    debuggingStore.updateStatus('screen', { status: 1, localStream: null, errorText: '初始化屏幕共享失败' });
                    sendError(402, 3);
                }
            });

        // --- 事件监听 ---
        localStream?.on('screen-sharing-stopped', () => {
            logger.warn('Screen sharing stopped by user.'); // 保留 logger
            // 保持代码 1 的状态更新和 unpublish 逻辑
            debuggingStore.updateStatus('screen', { status: 1, localStream: null, errorText: '用户停止共享，请重新共享屏幕' });
            localStream?.stop();
            if (localStream && client) {
                // 保持代码 1 的安全检查
                client.unpublish(localStream).catch((err) => logger.error('Error unpublishing screen stream after stop:', err));
            }
            sendError(203, 3);
            palyerStateIsOk = false;
            // 屏幕共享停止埋点 (与代码 2 一致)
            BossAnalyticsTrack('zhice-pc-exam-screen-stop', {
                pData: {
                    type: TrackTypeEnum.失败,
                    reportType: ReportTypeEnum.屏幕共享,
                    nameZh: '屏幕共享停止',
                },
            });
            // 埋点结束
        });

        client.on(client.TYPE.EVENT.CONNECTION_STATE_CHANGED, (evt: NEBULA.ConnectionStateChangedEvent) => {
            // 保持代码 1 更完善的状态处理 (兼容新旧状态名 + palyerStateIsOk 判断)
            if (evt?.state === 'disconnected' || evt.state === 'DISCONNECTED') {
                debuggingStore.updateStatus('screen', { status: 1, localStream: null, errorText: '网络异常，请刷新页面重试' });
                sendError(402, 3);
                palyerStateIsOk = false;
            } else if ((evt.state === 'reconnected' || evt.state === 'CONNECTED') && palyerStateIsOk) {
                debuggingStore.updateStatus('screen', { status: 2, errorText: '' });
                sendError(1, 3);
            } else if ((evt.state === 'reconnected' || evt.state === 'CONNECTED') && !palyerStateIsOk) {
                logger.warn(`Screen client reconnected, but player state is not OK. Status remains 1.`);
            }
            // 屏幕共享连接变化埋点 (恢复至代码 2 版本)
            BossAnalyticsTrack('zhice-pc-exam-screen-connection-change', {
                pData: {
                    type: TrackTypeEnum.失败, // 恢复为硬编码失败
                    message: evt.state, // 保持 evt.state
                    // prevState: evt.prevState, // 移除 prevState
                    reportType: ReportTypeEnum.屏幕共享,
                    nameZh: '屏幕共享连接变化', // 恢复 nameZh
                },
            });
            // 埋点结束
        });
    };

    /**
     * 处理屏幕共享和全屏功能的协调逻辑
     * @private
     * @async
     * @param {NEBULA.RtcStream} localStream - 屏幕共享流
     */
    private async handleScreenShareAndFullscreen(localStream: NEBULA.RtcStream): Promise<void> {
        try {
            // 获取视频轨道以检测共享类型
            const videoTrack = localStream.getVideoTrack();
            if (!videoTrack) {
                logger.warn('No video track found in screen share stream');
                return;
            }

            // 创建MediaStream以便使用detectScreenShareType
            const mediaStream = new MediaStream([videoTrack]);
            const shareType = detectScreenShareType(mediaStream);

            logger.info('Screen share type detected:', {
                shareType,
                trackSettings: videoTrack.getSettings()
            });

            // 检查是否需要全屏
            const debuggingStore = useDebuggingStore();
            if (debuggingStore.examConfig.switchScreenRule.fullScreen === 1) {
                await this.handleFullscreenForScreenShare(shareType);
            }
        } catch (error) {
            logger.error('Error handling screen share and fullscreen coordination:', error);
        }
    }

    /**
     * 为屏幕共享处理全屏逻辑
     * @private
     * @async
     * @param shareType 屏幕共享类型
     */
    private async handleFullscreenForScreenShare(shareType: 'tab' | 'window' | 'screen' | 'unknown'): Promise<void> {
        logger.info(`Handling fullscreen for screen share type: ${shareType}`);

        // 延迟尝试全屏，给屏幕共享一些时间稳定
        setTimeout(async () => {
            try {
                const success = await requestFullscreenWithScreenShare(undefined, {
                    isScreenSharing: true,
                    shareType,
                    delay: 500,
                    maxRetries: shareType === 'tab' ? 2 : 3
                });

                if (success) {
                    logger.info('Fullscreen request successful for screen share');
                    // 埋点记录成功
                    BossAnalyticsTrack('zhice-pc-exam-screen-fullscreen-success', {
                        pData: {
                            type: TrackTypeEnum.成功,
                            shareType,
                            reportType: ReportTypeEnum.屏幕共享,
                            nameZh: '屏幕共享全屏成功',
                        },
                    });
                } else {
                    logger.warn('Fullscreen request failed for screen share');
                    // 埋点记录失败
                    BossAnalyticsTrack('zhice-pc-exam-screen-fullscreen-failed', {
                        pData: {
                            type: TrackTypeEnum.失败,
                            shareType,
                            reportType: ReportTypeEnum.屏幕共享,
                            nameZh: '屏幕共享全屏失败',
                        },
                    });
                }
            } catch (error) {
                logger.error('Error requesting fullscreen for screen share:', error);
                // 埋点记录错误
                BossAnalyticsTrack('zhice-pc-exam-screen-fullscreen-error', {
                    pData: {
                        type: TrackTypeEnum.失败,
                        message: error instanceof Error ? error.message : String(error),
                        shareType,
                        reportType: ReportTypeEnum.屏幕共享,
                        nameZh: '屏幕共享全屏错误',
                    },
                });
            }
        }, 1000);
    }
}
